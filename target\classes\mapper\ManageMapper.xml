<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.ManageMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapManage" type="com.fruit.entity.Manage">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userName" column="userName" jdbcType="VARCHAR"/>
        <result property="passWord" column="passWord" jdbcType="VARCHAR"/>
        <result property="realName" column="realName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="Manage_field">
        id,userName,passWord,realName
    </sql>

    <!-- 实体类属性-->
    <sql id="Manage_insert">
        #{id},#{userName},#{passWord},#{realName}
    </sql>

    <!-- 更新结果  -->
    <sql id="Manage_update">
        <if test="userName != null">
            userName = #{userName},
        </if>
        <if test="passWord != null">
            passWord = #{passWord},
        </if>
        <if test="realName != null">
            realName = #{realName}
        </if>
    </sql>

    <!-- 查询时条件   -->
    <sql id="Manage_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="userName != null">
            and userName = #{userName}
        </if>
        <if test="passWord != null">
            and passWord = #{passWord}
        </if>
        <if test="realName != null">
            and realName = #{realName}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.Manage" useGeneratedKeys="true" keyProperty="id">
        insert into manage(
        <include refid="Manage_field"/>
        ) values(
        <include refid="Manage_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from manage where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.Manage">
        delete from manage where 1=1
        <include refid="Manage_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from manage where 1=1
        <include refid="Manage_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.Manage">
        update manage
        <set>
            <include refid="Manage_update"/>
        </set>
        where 1=1
            <include refid="Manage_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.Manage">
        update manage
        <set>
            <include refid="Manage_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapManage" parameterType="map">
        select <include refid="Manage_field"/>
        from manage where 1=1
        <include refid="Manage_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapManage">
        select <include refid="Manage_field"/>
        from manage
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapManage" parameterType="com.fruit.entity.Manage">
        select <include refid="Manage_field"/>
        from manage where 1=1
        <include refid="Manage_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapManage" parameterType="java.lang.Integer">
        select <include refid="Manage_field"/>
        from manage where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapManage" parameterType="java.lang.Integer">
        select <include refid="Manage_field"/>
        from manage where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapManage" parameterType="map">
        select <include refid="Manage_field"/>
        from manage where 1=1
        <include refid="Manage_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapManage" parameterType="com.fruit.entity.Manage">
        select <include refid="Manage_field"/>
        from manage where 1=1
        <include refid="Manage_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapManage" parameterType="map">
        select <include refid="Manage_field"/>
        from manage where 1=1
        <include refid="Manage_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapManage" parameterType="com.fruit.entity.Manage">
        select <include refid="Manage_field"/>
        from manage where 1=1
        <include refid="Manage_where"/>
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into manage(
        <include refid="Manage_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userName},#{item.passWord},#{item.realName})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update manage
            <set>
                <if test="item.userName != null">
                    userName = #{item.userName},
                </if>
                <if test="item.passWord != null">
                    passWord = #{item.passWord},
                </if>
                <if test="item.realName != null">
                    realName = #{item.realName}
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapManage">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapManage">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapManage">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>