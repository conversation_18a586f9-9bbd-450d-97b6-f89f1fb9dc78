<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.CarMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapCar" type="com.fruit.entity.Car">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="num" column="num" jdbcType="INTEGER"/>
        <result property="price" column="price" jdbcType="DECIMAL"/>
        <result property="total" column="total" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResultMapCarDto" type="com.fruit.entity.Car">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="num" column="num" jdbcType="INTEGER"/>
        <result property="price" column="price" jdbcType="DECIMAL"/>
        <result property="total" column="total" jdbcType="VARCHAR"/>
        <association property="item" column="item_id" select="com.fruit.mapper.ItemMapper.load"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="Car_field">
        id,item_id,user_id,num,price,total
    </sql>

    <!-- 实体类属性-->
    <sql id="Car_insert">
        #{id},#{itemId},#{userId},#{num},#{price},#{total}
    </sql>

    <!-- 更新结果  -->
    <sql id="Car_update">
        <if test="itemId != null">
            item_id = #{itemId},
        </if>
        <if test="userId != null">
            user_id = #{userId},
        </if>
        <if test="num != null">
            num = #{num},
        </if>
        <if test="price != null">
            price = #{price},
        </if>
        <if test="total != null">
            total = #{total},
        </if>

    </sql>

    <!-- 查询时条件   -->
    <sql id="Car_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="itemId != null">
            and item_id = #{itemId}
        </if>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="num != null">
            and num = #{num}
        </if>
        <if test="price != null">
            and price = #{price}
        </if>
        <if test="total != null">
            and total = #{total}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.Car" useGeneratedKeys="true" keyProperty="id">
        insert into car(
        <include refid="Car_field"/>
        ) values(
        <include refid="Car_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from car where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.Car">
        delete from car where 1=1
        <include refid="Car_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from car where 1=1
        <include refid="Car_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.Car">
        update car
        <set>
            <include refid="Car_update"/>
        </set>
        where 1=1
            <include refid="Car_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.Car">
        update car
        <set>
            <include refid="Car_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapCar" parameterType="map">
        select <include refid="Car_field"/>
        from car where 1=1
        <include refid="Car_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapCar">
        select <include refid="Car_field"/>
        from car
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapCar" parameterType="com.fruit.entity.Car">
        select <include refid="Car_field"/>
        from car where 1=1
        <include refid="Car_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapCar" parameterType="java.lang.Integer">
        select <include refid="Car_field"/>
        from car where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapCar" parameterType="java.lang.Integer">
        select <include refid="Car_field"/>
        from car where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapCar" parameterType="map">
        select <include refid="Car_field"/>
        from car where 1=1
        <include refid="Car_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapCar" parameterType="com.fruit.entity.Car">
        select <include refid="Car_field"/>
        from car where 1=1
        <include refid="Car_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapCar" parameterType="map">
        select <include refid="Car_field"/>
        from car where 1=1
        <include refid="Car_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapCar" parameterType="com.fruit.entity.Car">
        select <include refid="Car_field"/>
        from car where 1=1
        <include refid="Car_where"/>
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into car(
        <include refid="Car_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.item_id},#{item.user_id},#{item.num},#{item.price},#{item.total})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update car
            <set>
                <if test="item.itemId != null">
                    item_id = #{item.itemId},
                </if>
                <if test="item.userId != null">
                    user_id = #{item.userId},
                </if>
                <if test="item.num != null">
                    num = #{item.num},
                </if>
                <if test="item.price != null">
                    price = #{item.price},
                </if>
                <if test="item.total != null">
                    total = #{item.total},
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapCar">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapCarDto">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapCarDto">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>