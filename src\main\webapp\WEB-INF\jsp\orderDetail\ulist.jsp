<%@page language="java" contentType="text/html; character=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/taglibs.jsp"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>管理员后台</title>
    <link rel="stylesheet" href="${ctx}/resource/css/pintuer.css">
    <link rel="stylesheet" href="${ctx}/resource/css/admin.css">
    <script src="${ctx}/resource/js/jquery.js"></script>
    <script src="${ctx}/resource/js/pintuer.js"></script>
</head>
<body style="background-color: #f2f9fd">
    <div class="header bg-main">
        <div class="logo margin-big-left fadein-top">
            <h1>管理员后台</h1>
        </div>
        <div class="head-l">
            <a class="button button-little bg-green" href="${ctx}/login/uIndex" target="_blank">
                <span class="icon-home"></span>前台首页
            </a>
            <a class="button button-little bg-red" href="${ctx}/login/mtuichu">
                <span class="icon-power-off"></span>退出登录
            </a>
        </div>
    </div>
    <!-- 引入通用菜单栏 -->
    <%@include file="/common/admin_menu.jsp"%>
    <ul class="bread">
        <li><a href="${ctx}/itemCategory/tj" class="icon-home">首页</a> </li>
        <li><a href="${ctx}/itemOrder/findBySql">订单管理</a> </li>
        <li><a href="#">订单详情</a> </li>
    </ul>
    <div class="admin">
        <div class="panel admin-panel">
            <div class="padding border-bottom">
                <ul class="search" style="padding-left: 10px;">
                    <li>
                        <a class="button border-yellow" href="${ctx}/itemOrder/findBySql"><span class="icon-undo"></span> 返回订单列表</a>
                    </li>
                </ul>
            </div>
            <table class="table table-hover text-center">
                <tr>
                    <th>商品名称</th>
                    <th>商品主图</th>
                    <th>商品单价</th>
                    <th>购买数量</th>
                    <th>小计</th>
                    <th>状态</th>
                </tr>
            <c:forEach items="${pagers.datas}" var="data" varStatus="l">
                <tr>
                    <td>${data.item.name}</td>
                    <td><img src="${data.item.url1}" alt="" style="width: 100px;height: 100px;"></td>
                    <td>${data.item.price}</td>
                    <td>${data.num}</td>
                    <td>${data.total}</td>
                    <td style="color: red">
                        <c:if test="${data.status == 0}">
                                未退货
                        </c:if>
                        <c:if test="${data.status == 1}">
                            已退货
                        </c:if>
                    </td>

                </tr>
            </c:forEach>
                <tr>
                    <td colspan="8">
                        <div class="pagelist">
                            <!--分页开始-->
                            <pg:pager url="${ctx}/orderDetail/ulist?orderId=${obj.orderId}" maxIndexPages="5" items="${pagers.total}" maxPageItems="15" export="curPage=pageNumber">
                                <pg:last>
                                    共${pagers.total}记录，共${pageNumber}页，
                                </pg:last>
                                当前第${curPage}页
                                <pg:first>
                                    <a href="${pageUrl}">首页</a>
                                </pg:first>
                                <pg:prev>
                                    <a href="${pageUrl}">上一页</a>
                                </pg:prev>
                                <pg:pages>
                                    <c:choose>
                                        <c:when test="${curPage eq pageNumber}">
                                            <font color="red">[${pageNumber}]</font>
                                        </c:when>
                                        <c:otherwise>
                                            <a href="${pageUrl}">${pageNumber}</a>
                                        </c:otherwise>
                                    </c:choose>
                                </pg:pages>
                                <pg:next>
                                    <a href="${pageUrl}">下一页</a>
                                </pg:next>
                                <pg:last>
                                    <c:choose>
                                        <c:when test="${curPage eq pageNumber}">
                                            <font color="red">尾页</font>
                                        </c:when>
                                        <c:otherwise>
                                            <a href="${pageUrl}">尾页</a>
                                        </c:otherwise>
                                    </c:choose>
                                </pg:last>
                            </pg:pager>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>