-- 创建数据库
CREATE DATABASE IF NOT EXISTS fruitshop DEFAULT CHARACTER SET utf8mb4;
USE fruitshop;

-- 购物车表
CREATE TABLE car (
    id INT(10) NOT NULL COMMENT '主键',
    item_id INT(10) NULL COMMENT '商品ID',
    user_id INT(10) NULL COMMENT '用户ID',
    num INT(10) NULL COMMENT '商品数量',
    price DECIMAL(11,2) NULL COMMENT '单价',
    total VARCHAR(255) NULL COMMENT '总价',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- 评论表
CREATE TABLE comment (
    id INT(10) NOT NULL COMMENT '主键',
    user_id INT(10) NULL COMMENT '用户ID',
    item_id INT(10) NULL COMMENT '商品ID',
    content VARCHAR(255) NULL COMMENT '评论内容',
    addTime DATETIME NULL COMMENT '添加时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 商品信息表
CREATE TABLE item (
    id INT(10) NOT NULL COMMENT '主键',
    name VARCHAR(255) NULL COMMENT '商品名称',
    price VARCHAR(255) NULL COMMENT '商品价格',
    scNum INT(10) NULL COMMENT '收藏数',
    gmNum INT(10) NULL COMMENT '购买数',
    url1 VARCHAR(255) NULL COMMENT '图片1',
    url2 VARCHAR(255) NULL COMMENT '图片2',
    url3 VARCHAR(255) NULL COMMENT '图片3',
    url4 VARCHAR(255) NULL COMMENT '图片4',
    url5 VARCHAR(255) NULL COMMENT '图片5',
    ms TEXT NULL COMMENT '描述',
    pam1 VARCHAR(255) NULL COMMENT '参数1',
    pam2 VARCHAR(255) NULL COMMENT '参数2',
    pam3 VARCHAR(255) NULL COMMENT '参数3',
    val3 VARCHAR(255) NULL COMMENT '值3',
    val2 VARCHAR(255) NULL COMMENT '值2',
    val1 VARCHAR(255) NULL COMMENT '值1',
    type INT(10) NULL COMMENT '商品类型',
    zk INT(10) NULL COMMENT '折扣',
    category_id_one INT(10) NULL COMMENT '一级类别ID',
    category_id_two INT(10) NULL COMMENT '二级类别ID',
    isDelete INT(10) NULL COMMENT '是否删除 0否1是',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品信息表';

-- 商品类目表
CREATE TABLE item_category (
    id INT(10) NOT NULL COMMENT '主键',
    name VARCHAR(255) NULL COMMENT '类目名称',
    pid INT(10) NULL COMMENT '父类目ID',
    isDelete INT(10) NULL COMMENT '是否删除 0否1是',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品类目表';

-- 订单表
CREATE TABLE item_order (
    id INT(10) NOT NULL COMMENT '主键',
    item_id INT(10) NULL COMMENT '商品ID',
    user_id INT(10) NULL COMMENT '用户ID',
    code VARCHAR(255) NULL COMMENT '订单号',
    addTime DATETIME NULL COMMENT '下单时间',
    total VARCHAR(255) NULL COMMENT '订单总价',
    isDelete INT(10) NULL COMMENT '是否删除 0否1是',
    status INT(10) NULL COMMENT '订单状态 0新建1已取消2已发货3到收货4已评价',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 管理员表
CREATE TABLE manage (
    id INT(10) NOT NULL COMMENT '主键',
    userName VARCHAR(255) NULL COMMENT '用户名',
    passWord VARCHAR(255) NULL COMMENT '密码',
    realName VARCHAR(255) NULL COMMENT '真实姓名',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 留言表
CREATE TABLE message (
    id INT(10) NOT NULL COMMENT '主键',
    name VARCHAR(255) NULL COMMENT '留言人姓名',
    phone VARCHAR(255) NULL COMMENT '联系电话',
    content VARCHAR(255) NULL COMMENT '留言内容',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='留言表';

-- 公告表
CREATE TABLE news (
    id INT(10) NOT NULL COMMENT '主键',
    name VARCHAR(255) NULL COMMENT '公告标题',
    content TEXT NULL COMMENT '公告内容',
    addTime DATETIME NULL COMMENT '发布时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- 订单细节表
CREATE TABLE order_detail (
    id INT(10) NOT NULL COMMENT '主键',
    item_id INT(10) NULL COMMENT '商品ID',
    order_id INT(10) NULL COMMENT '订单ID',
    status INT(10) NULL COMMENT '退货状态 0未退货1已退货',
    num INT(10) NULL COMMENT '商品数量',
    total VARCHAR(255) NULL COMMENT '总价',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单细节表';

-- 收藏表
CREATE TABLE sc (
    id INT(10) NOT NULL COMMENT '主键',
    item_id INT(10) NULL COMMENT '商品ID',
    user_id INT(10) NULL COMMENT '用户ID',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏表';

-- 用户表
CREATE TABLE user (
    id INT(10) NOT NULL COMMENT '主键',
    userName VARCHAR(255) NULL COMMENT '用户名',
    passWord VARCHAR(255) NULL COMMENT '密码',
    phone VARCHAR(255) NULL COMMENT '电话',
    realName VARCHAR(255) NULL COMMENT '真实姓名',
    sex VARCHAR(255) NULL COMMENT '性别',
    address VARCHAR(255) NULL COMMENT '地址',
    email VARCHAR(255) NULL COMMENT '邮箱',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表'; 