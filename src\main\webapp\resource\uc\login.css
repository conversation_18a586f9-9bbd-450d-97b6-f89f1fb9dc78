body { background-color: #f5f5f5; min-width: 1140px; }

.wrapper { width: 1080px; }

.login-header { height: 96px; line-height: 96px; }
.login-header .logo { float: left; }
.login-header .zp { float: right; margin-top: 25px; font-size: 16px; line-height: 18px; text-align: center; color: #424242; }
.login-header .zp .ico { display: block; margin: 0 auto 5px; width: 25px; height: 29px; background: url(../img/login/zp.png); }

.login-footer { padding: 40px 0 100px; text-align: center; color: #8b8b8b; }
.login-footer .authentication { margin-top: 15px; font-size: 0; }
.login-footer .authentication a { display: inline-block; vertical-align: middle; margin: 0 5px; }

.check .sty1-checkbox { margin-right: 5px; }

.box-hd { overflow: hidden; margin-bottom: 3px; height: 58px; line-height: 58px; }
.box-hd .tit { float: left; font-size: 18px; color: #504d4d; }
.box-hd a { color: #999; float: right; }
.box-hd a:hover { color: #f34e4e; text-decoration: underline; }
.box-hd-tabs { height: auto; line-height: 62px; margin-bottom: 20px; }
.box-hd-tabs .item { float: left; padding: 18px 0; width: 50%; font-size: 18px; line-height: 1; text-align: center; color: #000; border-bottom: 2px solid #d3d3d3; }
.box-hd-tabs .item:hover { text-decoration: none; }
.box-hd-tabs .item.active { border-color: #f34e4e; }

.txtin-box { position: relative; display: block; margin-bottom: 10px; }
.txtin-box .txtin { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; display: block; padding: 10px; width: 100%; height: 52px; line-height: 30px; border: 1px solid #dedede; }

.tool { margin: 15px 0; line-height: 18px; font-size: 12px; }
.tool .check { float: left; color: #999; }
.tool .check input { margin-right: 5px; display: inline-block; vertical-align: middle; }
.tool .find { float: right; color: #999; }
.tool .find:hover { color: #f34e4e; text-decoration: underline; }

.tj { display: block; width: 100%; margin-top: 15px; height: 52px; line-height: 52px; color: #fff; font-size: 18px; text-align: center; background-color: #f34e4e; border: none; -webkit-border-radius: 2px; border-radius: 2px; }
.tj:hover { color: #fff; background: #f01e1e; }
.tj:active { background-color: #db0f0f; }

.other-way { margin-top: 29px; }
.other-way .item { float: left; margin-left: 50px; width: 70px; text-align: center; -webkit-transition: opacity 0.2s; transition: opacity 0.2s; }
.other-way .item.first { margin-left: 10px; }
.other-way .item .label { display: block; margin-top: 5px; color: #999; }
.other-way .item:hover { opacity: 0.8; filter: alpha(opacity=80); }

.login-main { height: 600px; }
.login-main-wrap { background: url(../img/login/login-bg.jpg); }

.login-box { float: right; margin: 60px 0 0 0; padding: 30px 38px 0; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; width: 406px; height: 474px; background-color: #fff; }
.login-box .txtin-box { position: relative; margin-bottom: 14px; }
.login-box .txtin-box .ico { position: absolute; width: 34px; height: 50px; }
.login-box .txtin-box .ico.user { background: url(../img/login/user.jpg) no-repeat 14px 17px; }
.login-box .txtin-box .ico.pwd { background: url(../img/login/pwd.jpg) no-repeat 15px 16px; }
.login-box .txtin-box .txtin { padding-left: 45px; }
.login-box .tj { margin-top: 20px; }

.main-wrap { padding: 55px 0; text-align: center; background: #fff; }

.center-box { width: 330px; display: inline-block; vertical-align: middle; text-align: left; }
.center-box .txtin-box-code .txtin { width: 194px; }
.center-box .txtin-box-code .get-yzm { position: absolute; top: 0; right: 0; width: 126px; height: 52px; line-height: 52px; text-align: center; color: #fff; background-color: #777; }
.center-box .txtin-box-code .yzm { position: absolute; top: 0; right: 0; width: 126px; height: 52px; }

/*# sourceMappingURL=login.css.map */
