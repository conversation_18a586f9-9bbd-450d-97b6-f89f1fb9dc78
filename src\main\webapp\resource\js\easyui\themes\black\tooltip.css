.tooltip {
  position: absolute;
  display: none;
  z-index: 9900000;
  outline: none;
  opacity: 1;
  filter: alpha(opacity=100);
  padding: 5px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.tooltip-content {
  font-size: 12px;
}
.tooltip-arrow-outer,
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 0;
  border-style: solid;
  border-width: 6px;
  border-color: transparent;
  _border-color: tomato;
  _filter: chroma(color=tomato);
}
.tooltip-arrow {
  display: none \9;
}
.tooltip-right .tooltip-arrow-outer {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -13px;
}
.tooltip-right .tooltip-arrow {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -12px;
}
.tooltip-left .tooltip-arrow-outer {
  right: 0;
  top: 50%;
  margin: -6px -13px 0 0;
}
.tooltip-left .tooltip-arrow {
  right: 0;
  top: 50%;
  margin: -6px -12px 0 0;
}
.tooltip-top .tooltip-arrow-outer {
  bottom: 0;
  left: 50%;
  margin: 0 0 -13px -6px;
}
.tooltip-top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin: 0 0 -12px -6px;
}
.tooltip-bottom .tooltip-arrow-outer {
  top: 0;
  left: 50%;
  margin: -13px 0 0 -6px;
}
.tooltip-bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin: -12px 0 0 -6px;
}
.tooltip {
  background-color: #666;
  border-color: #000;
  color: #fff;
}
.tooltip-right .tooltip-arrow-outer {
  border-right-color: #000;
}
.tooltip-right .tooltip-arrow {
  border-right-color: #666;
}
.tooltip-left .tooltip-arrow-outer {
  border-left-color: #000;
}
.tooltip-left .tooltip-arrow {
  border-left-color: #666;
}
.tooltip-top .tooltip-arrow-outer {
  border-top-color: #000;
}
.tooltip-top .tooltip-arrow {
  border-top-color: #666;
}
.tooltip-bottom .tooltip-arrow-outer {
  border-bottom-color: #000;
}
.tooltip-bottom .tooltip-arrow {
  border-bottom-color: #666;
}
