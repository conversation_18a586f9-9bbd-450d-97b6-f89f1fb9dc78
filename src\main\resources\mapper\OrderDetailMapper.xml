<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.OrderDetailMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapOrderDetail" type="com.fruit.entity.OrderDetail">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="num" column="num" jdbcType="INTEGER"/>
        <result property="total" column="total" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResultMapOrderDetailDto" type="com.fruit.entity.OrderDetail">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="num" column="num" jdbcType="INTEGER"/>
        <result property="total" column="total" jdbcType="VARCHAR"/>
        <association property="item" column="item_id" select="com.fruit.mapper.ItemMapper.load"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="OrderDetail_field">
        id,item_id,order_id,status,num,total
    </sql>

    <!-- 实体类属性-->
    <sql id="OrderDetail_insert">
        #{id},#{itemId},#{orderId},#{status},#{num},#{total}
    </sql>

    <!-- 更新结果  -->
    <sql id="OrderDetail_update">
        <if test="itemId != null">
            item_id = #{itemId},
        </if>
        <if test="orderId != null">
            order_id = #{orderId},
        </if>
        <if test="status != null">
            status = #{status}
        </if>
        <if test="num != null">
            num = #{num},
        </if>
        <if test="total != null">
            total = #{total},
        </if>

    </sql>

    <!-- 查询时条件   -->
    <sql id="OrderDetail_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="itemId != null">
            and item_id = #{itemId}
        </if>
        <if test="orderId != null">
            and order_id = #{orderId}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="num != null">
            and num = #{num}
        </if>
        <if test="total != null">
            and total = #{total}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.OrderDetail" useGeneratedKeys="true" keyProperty="id">
        insert into order_detail(
        <include refid="OrderDetail_field"/>
        ) values(
        <include refid="OrderDetail_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from order_detail where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.OrderDetail">
        delete from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.OrderDetail">
        update order_detail
        <set>
            <include refid="OrderDetail_update"/>
        </set>
        where 1=1
            <include refid="OrderDetail_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.OrderDetail">
        update order_detail
        <set>
            <include refid="OrderDetail_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapOrderDetail" parameterType="map">
        select <include refid="OrderDetail_field"/>
        from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapOrderDetail">
        select <include refid="OrderDetail_field"/>
        from order_detail
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapOrderDetail" parameterType="com.fruit.entity.OrderDetail">
        select <include refid="OrderDetail_field"/>
        from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapOrderDetail" parameterType="java.lang.Integer">
        select <include refid="OrderDetail_field"/>
        from order_detail where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapOrderDetail" parameterType="java.lang.Integer">
        select <include refid="OrderDetail_field"/>
        from order_detail where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapOrderDetail" parameterType="map">
        select <include refid="OrderDetail_field"/>
        from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapOrderDetail" parameterType="com.fruit.entity.OrderDetail">
        select <include refid="OrderDetail_field"/>
        from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapOrderDetail" parameterType="map">
        select <include refid="OrderDetail_field"/>
        from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapOrderDetail" parameterType="com.fruit.entity.OrderDetail">
        select <include refid="OrderDetail_field"/>
        from order_detail where 1=1
        <include refid="OrderDetail_where"/>
    </select>

    <select id="listByOrderId" resultMap="ResultMapOrderDetailDto">
        select <include refid="OrderDetail_field"/>
        from order_detail where order_id = #{id}
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into order_detail(
        <include refid="OrderDetail_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.itemId},#{item.orderId},#{item.status},#{item.num},#{item.total})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update order_detail
            <set>
                <if test="item.itemId != null">
                    item_id = #{item.itemId},
                </if>
                <if test="item.orderId != null">
                    order_id = #{item.orderId},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.num != null">
                    num = #{item.num},
                </if>
                <if test="item.total != null">
                    total = #{item.total},
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapOrderDetail">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapOrderDetailDto">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapOrderDetailDto">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>