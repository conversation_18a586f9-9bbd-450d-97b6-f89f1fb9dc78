.gray-box { background-color: #f5f5f5; }

.white-box { background-color: #fff; }

.section-hd { position: relative; margin: 35px 0 15px; height: 44px; line-height: 44px; }
.section-hd .title { padding-left: 4px; font-size: 24px; }
.section-hd .bar { position: absolute; top: 0; right: 0; }
.section-hd .more { line-height: 24px; }
.section-hd .more a { float: left; margin: 10px 0 10px 10px; color: #787878; }
.section-hd .more a:hover { border-bottom: 1px solid #000; }

.home-full-banner { position: relative; height: 480px; }
.home-full-banner .item { position: relative; display: block; height: 480px; -webkit-transform: translateZ(0); transform: translateZ(0); }
.home-full-banner .item img { position: absolute; top: 0; left: 50%; margin-left: -960px; width: 1920px; height: 480px; }
.home-full-banner .slick-dots { position: absolute; bottom: 20px; left: 0; width: 100%; height: 16px; line-height: 16px; text-align: center; font-size: 0; }
.home-full-banner .slick-dots > li { display: inline-block; vertical-align: middle; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; margin: 0 6px; width: 14px; height: 14px; border: 2px solid #fff; -webkit-border-radius: 50%; border-radius: 50%; }
.home-full-banner .slick-dots > li > button { display: none; }
.home-full-banner .slick-dots > li.slick-active { background: #f52f3e; border-color: #f52f3e; }

.promo-list { margin: 50px 0 30px; }
.promo-list .col { float: left; padding-left: 13px; }
.promo-list .col:first-child { padding-left: 0; }
.promo-list .item { display: block; }
.promo-list .item img { display: block; width: 290px; height: 184px; }

.flash-sale { margin: 0 0 -16px -16px; }
.flash-sale-section { padding-bottom: 50px; }
.flash-sale .grid { position: relative; float: left; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; margin: 0 0 16px 16px; overflow: hidden; line-height: 22px; background-color: #fff; }
.flash-sale .grid-b { width: 592px; height: 396px; background: transparent; }
.flash-sale .grid-b img { display: block; -webkit-transition: transform 0.5s; transition: transform 0.5s; -webkit-perspective: 1000; perspective: 1000; -webkit-backface-visibility: hidden; backface-visibility: hidden; }
.flash-sale .grid-b:hover img { -webkit-transform: scale(1.1); transform: scale(1.1); }
.flash-sale .grid-m { width: 288px; height: 396px; padding: 40px 25px; }
.flash-sale .grid-m img { width: 240px; height: 240px; bottom: 20px; right: 10px; }
.flash-sale .grid-s { padding: 40px 120px 0 24px; width: 288px; height: 190px; }
.flash-sale .grid-s img { width: 110px; height: 110px; bottom: 10px; right: 10px; }
.flash-sale .grid-m img, .flash-sale .grid-s img { position: absolute; -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; }
.flash-sale .grid-m:hover img, .flash-sale .grid-s:hover img { -webkit-transform: translateX(-5px); transform: translateX(-5px); }
.flash-sale .inner-text { position: relative; z-index: 1; }
.flash-sale .tit { font-size: 16px; color: #333; }
.flash-sale .stit { color: #666; }
.flash-sale .info { color: #ff4d4d; }

.brand-feast-section { padding: 20px 0 65px; }
.brand-feast .ad-box { float: left; width: 380px; height: 380px; border: 1px solid #eee; }
.brand-feast .ad-box img { width: 380px; height: 380px; }
.brand-feast-list { float: right; width: 798px; }
.brand-feast-list .item { position: relative; float: left; margin: 0 0 -1px -1px; width: 132px; height: 126px; text-align: center; border: 1px solid #eee; }
.brand-feast-list .item .logo { width: 132px; height: 126px; display: table-cell; vertical-align: middle; }
.brand-feast-list .item .logo img { max-width: 120px; max-height: 70px; }
.brand-feast-list .item .mask { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: #333; background-color: rgba(0, 0, 0, 0.8); font-size: 0; opacity: 0; filter: alpha(opacity=0); -webkit-transition: opacity 0.3s ease-out; transition: opacity 0.3s ease-out; }
.brand-feast-list .item .mask:after { content: ''; height: 100%; display: inline-block; vertical-align: middle; }
.brand-feast-list .item .mask .text { display: inline-block; vertical-align: middle; font-size: 13px; color: #fff; }
.brand-feast-list .item .mask:hover { opacity: 1; filter: alpha(opacity=100); }

.floor-wrap { padding: 20px 0 65px; }
.floor-section .section-bd { border-top: 1px solid #f5f5f5; }
.floor-section .section-side { position: relative; float: left; overflow: hidden; width: 268px; height: 597px; }
.floor-section .section-side .banner a { display: block; height: 597px; }
.floor-section .section-side .brands { position: absolute; left: 0; bottom: 0; width: 100%; height: 200px; overflow: hidden; background-color: #fff; }
.floor-section .section-side .brands .item { float: left; margin: 0 -1px -1px; border-left: 1px solid #f3f3f3; border-bottom: 1px solid #f3f3f3; }
.floor-section .section-side .brands .inner { display: table-cell; width: 134px; height: 67px; vertical-align: middle; text-align: center; }
.floor-section .section-side .brands .inner img { max-width: 110px; max-height: 50px; }
.floor-section .section-side .category { position: absolute; left: 0; bottom: 0; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; width: 100%; height: 225px; padding: 0 24px; color: #fff; }
.floor-section .section-side .category .tit { margin-bottom: 10px; padding-left: 8px; font-size: 18px; }
.floor-section .section-side .category .row { margin-left: -8px; }
.floor-section .section-side .category .col { padding: 0 0 8px 8px; }
.floor-section .section-side .category .item { display: block; line-height: 34px; text-align: center; color: #fff; }
.floor-section .section-cont { margin-left: 268px; }
.floor-section .section-cont .col { float: left; margin: 1px -1px 0 0; overflow: hidden; }
.floor-section .section-cont .col .grid { position: relative; display: block; margin-top: -1px; background-color: #fff; border: 1px solid #f0f0f0; }
.floor-section .section-cont .col .grid:first-child { border-top: 0; }
.floor-section .section-cont .inner-text { position: relative; padding: 33px 28px; }
.floor-section .section-cont .inner-text .tit { font-size: 16px; line-height: 24px; color: #333; }
.floor-section .section-cont .inner-text .info { color: #f52f3e; }
.floor-section .section-cont .grid-b { width: 349px; height: 397px; background-color: #fff; }
.floor-section .section-cont .grid-m { width: 349px; height: 198px; }
.floor-section .section-cont .grid-s { width: 290px; height: 198px; }
.floor-section .section-cont .grid-b img, .floor-section .section-cont .grid-m img, .floor-section .section-cont .grid-s img { position: absolute; -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; }
.floor-section .section-cont .grid-b:hover img, .floor-section .section-cont .grid-m:hover img, .floor-section .section-cont .grid-s:hover img { -webkit-transform: translateX(-5px); transform: translateX(-5px); }
.floor-section .section-cont .grid-s img { width: 100px; height: 100px; bottom: 10px; right: 10px; }
.floor-section .section-cont .grid-m img { width: 120px; height: 120px; bottom: 10px; right: 10px; }
.floor-section .section-cont .grid-b img { width: 240px; height: 240px; right: 20px; bottom: 10px; }
.floor-section .section-cont .grid-b .inner-text .tit { margin-bottom: 3px; font-size: 24px; line-height: 30px; }
.floor-section.floor1 .section-bd { border-top-color: #7a72e0; }
.floor-section.floor1 .section-side { background-color: #7a72e0; }
.floor-section.floor1 .section-side .category .item { background-color: #9d98e8; }
.floor-section.floor1 .section-side .category .item:hover { background-color: #a9a4eb; }
.floor-section.floor2 .section-bd { border-top-color: #ffbb57; }
.floor-section.floor2 .section-side { background-color: #ffbb57; }
.floor-section.floor2 .section-side .category .item { background-color: #ffce85; }
.floor-section.floor2 .section-side .category .item:hover { background-color: #ffd494; }
.floor-section.floor3 .section-bd { border-top-color: #5dc7fa; }
.floor-section.floor3 .section-side { background-color: #5dc7fa; }
.floor-section.floor3 .section-side .category .item { background-color: #8ad6fb; }
.floor-section.floor3 .section-side .category .item:hover { background-color: #98dcfc; }
.floor-section.floor4 .section-bd { border-top-color: #ff3e4d; }
.floor-section.floor4 .section-side { background-color: #ff3e4d; }
.floor-section.floor4 .section-side .category .item { background-color: #ff6c77; }
.floor-section.floor4 .section-side .category .item:hover { background-color: #ff7b85; }
.floor-section.floor5 .section-bd { border-top-color: #8dc82f; }
.floor-section.floor5 .section-side { background-color: #8dc82f; }
.floor-section.floor5 .section-side .category .item { background-color: #a2d64f; }
.floor-section.floor5 .section-side .category .item:hover { background-color: #a8d95c; }

/*# sourceMappingURL=home.css.map */
