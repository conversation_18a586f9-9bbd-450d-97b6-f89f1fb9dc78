spring.servlet.multipart.max-file-size=10MB
spring.mvc.view.prefix=/WEB-INF/jsp/
spring.mvc.view.suffix=.jsp
#\u6570\u636E\u6E90\u76F8\u5173\u914D\u7F6E driverClassName url username password
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*********************************************************************
spring.datasource.username=root
spring.datasource.password=03230920

#\u8BBE\u7F6Emybatis\u7684mapper\u6587\u4EF6\u6240\u5728\u8DEF\u5F84 \u522B\u540D
mybatis.mapper-locations=classpath:mapper/*Mapper.xml
mybatis.type-aliases-package=com.fruit.entity
#\u5F00\u542Fjsp\u9875\u9762\u7684\u70ED\u90E8\u7F72
server.servlet.jsp.init-parameters.development=true
#pagehelper
pagehelper.auto-dialect=true
pagehelper.reasonable=true
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql

server.port=8081
server.servlet.context-path=/