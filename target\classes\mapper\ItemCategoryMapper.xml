<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.ItemCategoryMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapItemCategory" type="com.fruit.entity.ItemCategory">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="pid" column="pid" jdbcType="INTEGER"/>
        <result property="isDelete" column="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="ItemCategory_field">
        id,name,pid,isDelete
    </sql>

    <!-- 实体类属性-->
    <sql id="ItemCategory_insert">
        #{id},#{name},#{pid},#{isDelete}
    </sql>

    <!-- 更新结果  -->
    <sql id="ItemCategory_update">
        <if test="name != null">
            name = #{name},
        </if>
        <if test="pid != null">
            pid = #{pid},
        </if>
        <if test="isDelete != null">
            isDelete = #{isDelete}
        </if>
    </sql>

    <!-- 查询时条件   -->
    <sql id="ItemCategory_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="name != null">
            and name = #{name}
        </if>
        <if test="pid != null">
            and pid = #{pid}
        </if>
        <if test="isDelete != null">
            and isDelete = #{isDelete}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.ItemCategory" useGeneratedKeys="true" keyProperty="id">
        insert into item_category(
        <include refid="ItemCategory_field"/>
        ) values(
        <include refid="ItemCategory_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from item_category where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.ItemCategory">
        delete from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.ItemCategory">
        update item_category
        <set>
            <include refid="ItemCategory_update"/>
        </set>
        where 1=1
            <include refid="ItemCategory_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.ItemCategory">
        update item_category
        <set>
            <include refid="ItemCategory_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapItemCategory" parameterType="map">
        select <include refid="ItemCategory_field"/>
        from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapItemCategory">
        select <include refid="ItemCategory_field"/>
        from item_category
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapItemCategory" parameterType="com.fruit.entity.ItemCategory">
        select <include refid="ItemCategory_field"/>
        from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapItemCategory" parameterType="java.lang.Integer">
        select <include refid="ItemCategory_field"/>
        from item_category where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapItemCategory" parameterType="java.lang.Integer">
        select <include refid="ItemCategory_field"/>
        from item_category where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapItemCategory" parameterType="map">
        select <include refid="ItemCategory_field"/>
        from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapItemCategory" parameterType="com.fruit.entity.ItemCategory">
        select <include refid="ItemCategory_field"/>
        from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapItemCategory" parameterType="map">
        select <include refid="ItemCategory_field"/>
        from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapItemCategory" parameterType="com.fruit.entity.ItemCategory">
        select <include refid="ItemCategory_field"/>
        from item_category where 1=1
        <include refid="ItemCategory_where"/>
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into item_category(
        <include refid="ItemCategory_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.name},#{item.pid},#{item.isDelete})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update item_category
            <set>
                <if test="item.name != null">
                    name = #{item.name},
                </if>
                <if test="item.pid != null">
                    pid = #{item.pid},
                </if>
                <if test="item.isDelete != null">
                    isDelete = #{item.isDelete}
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapItemCategory">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapItemCategory">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapItemCategory">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>