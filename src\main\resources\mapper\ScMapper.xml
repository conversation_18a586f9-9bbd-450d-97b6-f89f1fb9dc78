<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.ScMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapSc" type="com.fruit.entity.Sc">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="ResultMapScDto" type="com.fruit.entity.Sc">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <association property="item" column="item_id" select="com.fruit.mapper.ItemMapper.load"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="Sc_field">
        id,item_id,user_id
    </sql>

    <!-- 实体类属性-->
    <sql id="Sc_insert">
        #{id},#{itemId},#{userId}
    </sql>

    <!-- 更新结果  -->
    <sql id="Sc_update">
        <if test="itemId != null">
            item_id = #{itemId},
        </if>
        <if test="userId != null">
            user_id = #{userId},
        </if>

    </sql>

    <!-- 查询时条件   -->
    <sql id="Sc_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="itemId != null">
            and item_id = #{itemId}
        </if>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.Sc" useGeneratedKeys="true" keyProperty="id">
        insert into sc(
        <include refid="Sc_field"/>
        ) values(
        <include refid="Sc_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from sc where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.Sc">
        delete from sc where 1=1
        <include refid="Sc_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from sc where 1=1
        <include refid="Sc_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.Sc">
        update sc
        <set>
            <include refid="Sc_update"/>
        </set>
        where 1=1
            <include refid="Sc_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.Sc">
        update sc
        <set>
            <include refid="Sc_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapSc" parameterType="map">
        select <include refid="Sc_field"/>
        from sc where 1=1
        <include refid="Sc_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapSc">
        select <include refid="Sc_field"/>
        from sc
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapSc" parameterType="com.fruit.entity.Sc">
        select <include refid="Sc_field"/>
        from sc where 1=1
        <include refid="Sc_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapSc" parameterType="java.lang.Integer">
        select <include refid="Sc_field"/>
        from sc where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapSc" parameterType="java.lang.Integer">
        select <include refid="Sc_field"/>
        from sc where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapSc" parameterType="map">
        select <include refid="Sc_field"/>
        from sc where 1=1
        <include refid="Sc_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapSc" parameterType="com.fruit.entity.Sc">
        select <include refid="Sc_field"/>
        from sc where 1=1
        <include refid="Sc_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapSc" parameterType="map">
        select <include refid="Sc_field"/>
        from sc where 1=1
        <include refid="Sc_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapSc" parameterType="com.fruit.entity.Sc">
        select <include refid="Sc_field"/>
        from sc where 1=1
        <include refid="Sc_where"/>
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into sc(
        <include refid="Sc_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.item_id},#{item.user_id})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update sc
            <set>
                <if test="item.itemId != null">
                    item_id = #{item.itemId},
                </if>
                <if test="item.userId != null">
                    user_id = #{item.userId},
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapSc">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapScDto">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapScDto">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>