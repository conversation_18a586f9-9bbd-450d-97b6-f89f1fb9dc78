<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.ItemMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapItem" type="com.fruit.entity.Item">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="price" column="price" jdbcType="VARCHAR"/>
        <result property="zk" column="zk" jdbcType="INTEGER"/>
        <result property="scNum" column="scNum" jdbcType="INTEGER"/>
        <result property="gmNum" column="gmNum" jdbcType="INTEGER"/>
        <result property="url1" column="url1" jdbcType="VARCHAR"/>
        <result property="url2" column="url2" jdbcType="VARCHAR"/>
        <result property="url3" column="url3" jdbcType="VARCHAR"/>
        <result property="url4" column="url4" jdbcType="VARCHAR"/>
        <result property="url5" column="url5" jdbcType="VARCHAR"/>
        <result property="ms" column="ms" jdbcType="VARCHAR"/>
        <result property="pam1" column="pam1" jdbcType="VARCHAR"/>
        <result property="pam2" column="pam2" jdbcType="VARCHAR"/>
        <result property="pam3" column="pam3" jdbcType="VARCHAR"/>
        <result property="val1" column="val1" jdbcType="VARCHAR"/>
        <result property="val2" column="val2" jdbcType="VARCHAR"/>
        <result property="val3" column="val3" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="categoryIdOne" column="category_id_one" jdbcType="INTEGER"/>
        <result property="categoryIdTwo" column="category_id_two" jdbcType="INTEGER"/>
        <result property="isDelete" column="isDelete" jdbcType="INTEGER"/>
        <association property="yiji" column="category_id_one" select="com.fruit.mapper.ItemCategoryMapper.load"/>
        <association property="erji" column="category_id_two" select="com.fruit.mapper.ItemCategoryMapper.load"/>
        <collection property="pls" column="id" ofType="com.fruit.entity.Comment" select="com.fruit.mapper.CommentMapper.listByItemId"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="Item_field">
        id,name,price,zk,scNum,gmNum,url1,url2,url3,url4,url5,ms,pam1,pam2,pam3,val1,val2,val3,type,category_id_one,category_id_two,isDelete
    </sql>

    <!-- 实体类属性-->
    <sql id="Item_insert">
        #{id},#{name},#{price},#{zk},#{scNum},#{gmNum},#{url1},#{url2},#{url3},#{url4},#{url5},#{ms},#{pam1},#{pam2},#{pam3},
        #{val1},#{val2},#{val3},#{type},#{categoryIdOne},#{categoryIdTwo},#{isDelete}
    </sql>

    <!-- 更新结果  -->
    <sql id="Item_update">
        <if test="name != null">
            name = #{name},
        </if>
        <if test="price != null">
            price = #{price},
        </if>
        <if test="zk != null">
            zk = #{zk},
        </if>
        <if test="scNum != null">
            scNum = #{scNum},
        </if>
        <if test="gmNum != null">
            gmNum = #{gmNum},
        </if>
        <if test="url1 != null">
            url1 = #{url1},
        </if>
        <if test="url2 != null">
            url2 = #{url2},
        </if>
        <if test="url3 != null">
            url3 = #{url3},
        </if>
        <if test="url4 != null">
            url4 = #{url4},
        </if>
        <if test="url5 != null">
            url5 = #{url5},
        </if>
        <if test="ms != null">
            ms = #{ms},
        </if>
        <if test="pam1 != null">
            pam1 = #{pam1},
        </if>
        <if test="pam2 != null">
            pam2 = #{pam2},
        </if>
        <if test="pam3 != null">
            pam3 = #{pam3},
        </if>
        <if test="val1 != null">
            val1 = #{val1},
        </if>
        <if test="val2 != null">
            val2 = #{val2},
        </if>
        <if test="val3 != null">
            val3 = #{val3},
        </if>
        <if test="type != null">
            type = #{type},
        </if>
        <if test="categoryIdOne != null">
            category_id_one = #{categoryIdOne},
        </if>
        <if test="categoryIdTwo != null">
            category_id_two = #{categoryIdTwo},
        </if>
        <if test="isDelete != null">
            isDelete = #{isDelete}
        </if>
    </sql>

    <!-- 查询时条件   -->
    <sql id="Item_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="name != null">
            and name = #{name}
        </if>
        <if test="price != null">
            and price = #{price}
        </if>
        <if test="zk != null">
            and zk = #{zk}
        </if>
        <if test="scNum != null">
            and scNum = #{scNum}
        </if>
        <if test="gmNum != null">
            and gmNum = #{gmNum}
        </if>
        <if test="url1 != null">
            and url1 = #{url1}
        </if>
        <if test="url2 != null">
            and url2 = #{url2}
        </if>
        <if test="url3 != null">
            and url3 = #{url3}
        </if>
        <if test="url4 != null">
            and url4 = #{url4}
        </if>
        <if test="url5 != null">
            and url5 = #{url5}
        </if>
        <if test="ms != null">
            and ms = #{ms}
        </if>
        <if test="pam1 != null">
            and pam1 = #{pam1}
        </if>
        <if test="pam2 != null">
            and pam2 = #{pam2}
        </if>
        <if test="pam3 != null">
            and pam3 = #{pam3}
        </if>
        <if test="val1 != null">
            and val1 = #{val1}
        </if>
        <if test="val2 != null">
            and val2 = #{val2}
        </if>
        <if test="val3 != null">
            and val3 = #{val3}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="categoryIdOne != null">
            and category_id_one = #{categoryIdOne}
        </if>
        <if test="categoryIdTwo != null">
            and category_id_two = #{categoryIdTwo}
        </if>
        <if test="isDelete != null">
            and isDelete = #{isDelete}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.Item" useGeneratedKeys="true" keyProperty="id">
        insert into item(
        <include refid="Item_field"/>
        ) values(
        <include refid="Item_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from item where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.Item">
        delete from item where 1=1
        <include refid="Item_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from item where 1=1
        <include refid="Item_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.Item">
        update item
        <set>
            <include refid="Item_update"/>
        </set>
        where 1=1
            <include refid="Item_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.Item">
        update item
        <set>
            <include refid="Item_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapItem" parameterType="map">
        select <include refid="Item_field"/>
        from item where 1=1
        <include refid="Item_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapItem">
        select <include refid="Item_field"/>
        from item
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapItem" parameterType="com.fruit.entity.Item">
        select <include refid="Item_field"/>
        from item where 1=1
        <include refid="Item_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapItem" parameterType="java.lang.Integer">
        select <include refid="Item_field"/>
        from item where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapItem" parameterType="java.lang.Integer">
        select <include refid="Item_field"/>
        from item where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapItem" parameterType="map">
        select <include refid="Item_field"/>
        from item where 1=1
        <include refid="Item_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapItem" parameterType="com.fruit.entity.Item">
        select <include refid="Item_field"/>
        from item where 1=1
        <include refid="Item_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapItem" parameterType="map">
        select <include refid="Item_field"/>
        from item where 1=1
        <include refid="Item_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapItem" parameterType="com.fruit.entity.Item">
        select <include refid="Item_field"/>
        from item where 1=1
        <include refid="Item_where"/>
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into item(
        <include refid="Item_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.name},#{item.price},#{item.zk},#{item.scNum},#{item.gmNum},#{item.url1},#{item.url2},#{item.url3},#{item.url4},#{item.url5},#{item.ms},#{item.pam1},#{item.pam2},#{item.pam3},
            #{val1},#{item.val2},#{item.val3},#{item.type},#{item.categoryIdOne},#{item.categoryIdTwo},#{item.isDelete})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update item
            <set>
                <if test="item.name != null">
                    name = #{item.name},
                </if>
                <if test="item.price != null">
                    price = #{item.price},
                </if>
                <if test="item.zk != null">
                    zk = #{item.zk},
                </if>
                <if test="item.scNum != null">
                    scNum = #{item.scNum},
                </if>
                <if test="item.gmNum != null">
                    gmNum = #{item.gmNum},
                </if>
                <if test="item.url1 != null">
                    url1 = #{item.url1},
                </if>
                <if test="item.url2 != null">
                    url2 = #{item.url2},
                </if>
                <if test="item.url3 != null">
                    url3 = #{item.url3},
                </if>
                <if test="item.url4 != null">
                    url4 = #{item.url4},
                </if>
                <if test="item.url5 != null">
                    url5 = #{item.url5},
                </if>
                <if test="item.ms != null">
                    ms = #{item.ms},
                </if>
                <if test="item.pam1 != null">
                    pam1 = #{item.pam1},
                </if>
                <if test="item.pam2 != null">
                    pam2 = #{item.pam2},
                </if>
                <if test="item.pam3 != null">
                    pam3 = #{item.pam3},
                </if>
                <if test="item.val1 != null">
                    val1 = #{item.val1},
                </if>
                <if test="item.val2 != null">
                    val2 = #{item.val2},
                </if>
                <if test="item.val3 != null">
                    val3 = #{item.val3},
                </if>
                <if test="item.type != null">
                    type = #{item.type},
                </if>
                <if test="item.categoryIdOne != null">
                    category_id_one = #{item.categoryIdOne},
                </if>
                <if test="item.categoryIdTwo != null">
                    category_id_two = #{item.categoryIdTwo},
                </if>
                <if test="item.isDelete != null">
                    isDelete = #{item.isDelete}
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapItem">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapItem">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapItem">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>