<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.CommentMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapComment" type="com.fruit.entity.Comment">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="userId" jdbcType="INTEGER"/>
        <result property="itemId" column="itemId" jdbcType="INTEGER"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="addTime" column="addTime" jdbcType="TIMESTAMP"/>
        <association property="user" column="user_id" select="com.fruit.mapper.UserMapper.load"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="Comment_field">
        id,user_id,item_id,content,addTime
    </sql>

    <!-- 实体类属性-->
    <sql id="Comment_insert">
        #{id},#{userId},#{itemId},#{content},#{addTime}
    </sql>

    <!-- 更新结果  -->
    <sql id="Comment_update">
        <if test="userId != null">
            user_id = #{userId},
        </if>
        <if test="itemId != null">
            item_id = #{itemId},
        </if>
        <if test="content != null">
            content = #{content},
        </if>
        <if test="addTime != null">
            addTime = #{addTime}
        </if>
    </sql>

    <!-- 查询时条件   -->
    <sql id="Comment_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="itemId != null">
            and item_id = #{itemId}
        </if>
        <if test="content != null">
            and content = #{content}
        </if>
        <if test="addTime != null">
            and addTime = #{addTime}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.Comment" useGeneratedKeys="true" keyProperty="id">
        insert into comment(
        <include refid="Comment_field"/>
        ) values(
        <include refid="Comment_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from comment where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.Comment">
        delete from comment where 1=1
        <include refid="Comment_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from comment where 1=1
        <include refid="Comment_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.Comment">
        update comment
        <set>
            <include refid="Comment_update"/>
        </set>
        where 1=1
            <include refid="Comment_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.Comment">
        update comment
        <set>
            <include refid="Comment_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapComment" parameterType="map">
        select <include refid="Comment_field"/>
        from comment where 1=1
        <include refid="Comment_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapComment">
        select <include refid="Comment_field"/>
        from comment
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapComment" parameterType="com.fruit.entity.Comment">
        select <include refid="Comment_field"/>
        from comment where 1=1
        <include refid="Comment_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapComment" parameterType="java.lang.Integer">
        select <include refid="Comment_field"/>
        from comment where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapComment" parameterType="java.lang.Integer">
        select <include refid="Comment_field"/>
        from comment where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapComment" parameterType="map">
        select <include refid="Comment_field"/>
        from comment where 1=1
        <include refid="Comment_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapComment" parameterType="com.fruit.entity.Comment">
        select <include refid="Comment_field"/>
        from comment where 1=1
        <include refid="Comment_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapComment" parameterType="map">
        select <include refid="Comment_field"/>
        from comment where 1=1
        <include refid="Comment_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapComment" parameterType="com.fruit.entity.Comment">
        select <include refid="Comment_field"/>
        from comment where 1=1
        <include refid="Comment_where"/>
    </select>

    <!--根据商品id查询对象列表-->
    <select id="listByItemId" resultMap="ResultMapComment">
        select <include refid="Comment_field"/>
        from comment where item_id = #{id} order by id desc
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into comment(
        <include refid="Comment_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId},#{item.itemId},#{item.content},#{item.addTime})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update comment
            <set>
                <if test="item.userId != null">
                    user_id = #{item.userId},
                </if>
                <if test="item.itemId != null">
                    item_id = #{item.itemId},
                </if>
                <if test="item.content != null">
                    content = #{item.content},
                </if>
                <if test="item.addTime != null">
                    addTime = #{item.addTime}
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapComment">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapComment">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapComment">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>