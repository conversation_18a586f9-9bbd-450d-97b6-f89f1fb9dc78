<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fruit.mapper.MessageMapper">
    <!--实体类与数据库映射字段部分-->
    <resultMap id="ResultMapMessage" type="com.fruit.entity.Message">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 声明数据库字段 -->
    <sql id="Message_field">
        id,name,content,phone
    </sql>

    <!-- 实体类属性-->
    <sql id="Message_insert">
        #{id},#{name},#{content},#{phone}
    </sql>

    <!-- 更新结果  -->
    <sql id="Message_update">
        <if test="name != null">
            name = #{name},
        </if>
        <if test="content != null">
            content = #{content},
        </if>
        <if test="phone != null">
            phone = #{phone}
        </if>
    </sql>

    <!-- 查询时条件   -->
    <sql id="Message_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="name != null">
            and name = #{name}
        </if>
        <if test="content != null">
            and content = #{content}
        </if>
        <if test="phone != null">
            and phone = #{phone}
        </if>
    </sql>

    <!--    新增        -->
    <!--    参数：实体类-->
    <!--    返回：主键 -->
    <insert id="insert" parameterType="com.fruit.entity.Message" useGeneratedKeys="true" keyProperty="id">
        insert into message(
        <include refid="Message_field"/>
        ) values(
        <include refid="Message_insert"/>
        )
    </insert>

    <!-- 根据实体主键删除一个实体-->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from message where id=#{id}
    </delete>

    <!-- 通过实体删除-->
    <delete id="deleteByEntity" parameterType="com.fruit.entity.Message">
        delete from message where 1=1
        <include refid="Message_where"/>
    </delete>

    <!-- 通过map删除 -->
    <delete id="deleteByMap" parameterType="java.util.HashMap">
        delete from message where 1=1
        <include refid="Message_where"/>
    </delete>

    <!-- 更新一个实体 -->
    <update id="update" parameterType="com.fruit.entity.Message">
        update message
        <set>
            <include refid="Message_update"/>
        </set>
        where 1=1
            <include refid="Message_where"/>
    </update>

    <!-- 通过id进行修改-->
    <update id="updateById" parameterType="com.fruit.entity.Message">
        update message
        <set>
            <include refid="Message_update"/>
        </set>
        where id=#{id}
    </update>

    <!-- 根据参数查询-->
    <select id="listByMap" resultMap="ResultMapMessage" parameterType="map">
        select <include refid="Message_field"/>
        from message where 1=1
        <include refid="Message_where"/>
    </select>

    <!-- 查询整个表 -->
    <select id="listAll" resultMap="ResultMapMessage">
        select <include refid="Message_field"/>
        from message
    </select>

    <!-- 查询所有实体,根据实体属性值为判断条件查询所有实体-->
    <select id="listAllByEntity" resultMap="ResultMapMessage" parameterType="com.fruit.entity.Message">
        select <include refid="Message_field"/>
        from message where 1=1
        <include refid="Message_where"/>
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="load" resultMap="ResultMapMessage" parameterType="java.lang.Integer">
        select <include refid="Message_field"/>
        from message where id=#{id}
    </select>

    <!-- 根据主键获取一个实体-->
    <select id="getById" resultMap="ResultMapMessage" parameterType="java.lang.Integer">
        select <include refid="Message_field"/>
        from message where id=#{id}
    </select>

    <!-- 通过map查询-->
    <select id="getByMap" resultMap="ResultMapMessage" parameterType="map">
        select <include refid="Message_field"/>
        from message where 1=1
        <include refid="Message_where"/>
    </select>

    <!--通过对象查询-不分页-->
    <select id="getByEntity" resultMap="ResultMapMessage" parameterType="com.fruit.entity.Message">
        select <include refid="Message_field"/>
        from message where 1=1
        <include refid="Message_where"/>
    </select>

    <!-- 通过map查询分页-->
    <select id="findByMap" resultMap="ResultMapMessage" parameterType="map">
        select <include refid="Message_field"/>
        from message where 1=1
        <include refid="Message_where"/>
    </select>

    <!--通过对象查询分页-->
    <select id="findByEntity" resultMap="ResultMapMessage" parameterType="com.fruit.entity.Message">
        select <include refid="Message_field"/>
        from message where 1=1
        <include refid="Message_where"/>
    </select>

    <!-- 批量新增-->
    <select id="insertBatch" parameterType="java.util.List">
        insert into message(
        <include refid="Message_field"/>
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.name},#{item.content},#{item.phone})
        </foreach>
    </select>

    <!-- 批量修改-->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update message
            <set>
                <if test="item.name != null">
                    name = #{item.name},
                </if>
                <if test="item.content != null">
                    content = #{item.content},
                </if>
                <if test="item.phone != null">
                    phone = #{item.phone}
                </if>
            </set>
            where 1=1
            <if test="item.id != null">
               and id = #{item.id}
            </if>
        </foreach>
    </update>

    <!-- 封装纯sql语法-->
    <!-- 查询一个对象返回map-->
    <select id="getBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询一个对象返回实体类-->
    <select id="getBySqlReturnEntity" resultMap="ResultMapMessage">
        ${sql}
    </select>

    <!-- 查询一个对象返回map列表-->
    <select id="listBySqlReturnMap" resultType="map">
        ${sql}
    </select>

    <!-- 查询列表返回实体-->
    <select id="listBySqlReturnEntity" resultMap="ResultMapMessage">
        ${sql}
    </select>

    <!-- 查询分页-->
    <select id="findBySqlRerturnEntity" resultMap="ResultMapMessage">
        ${sql}
    </select>

    <!-- 通过sql修改-->
    <update id="updateBysql">
        ${sql}
    </update>

    <!-- 通过sql删除-->
    <delete id="deleteBySql">
         ${sql}
    </delete>
</mapper>